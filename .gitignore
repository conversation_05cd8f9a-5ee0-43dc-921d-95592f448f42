# -----------------------------
# Node / Vue / Vite
# -----------------------------
node_modules/
dist/
dist-ssr
.env
.env.*
!.env.example

# Vite cache
.vite/
.cache/
*.local
coverage

# Log files
logs
*.log
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
npm-debug.log*
yarn-debug.log*

# -----------------------------
# Capacitor
# -----------------------------
# iOS platform (если вдруг добавишь)
ios/
# Android build output
android/build/
android/app/build/
android/.gradle/
android/.idea/
android/local.properties
android/captures/
android/.DS_Store

# Optional: remove if you want to commit native code

# -----------------------------
# WebStorm / JetBrains IDE
# -----------------------------
.idea/
*.iml
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

# -----------------------------
# OS-specific
# -----------------------------
.DS_Store
Thumbs.db

/cypress/videos/
/cypress/screenshots/

/cert/*.pem
