# Layouts

Этот каталог содержит макеты (layouts) для различных типов страниц в приложении.

## MainLayout.vue

**Назначение:** Основной макет для форм и меню с градиентным фоном и белым окном по центру.

**Используется в:**
- AuthView (авторизация)
- MainMenuView (главное меню)
- CreateSessionView (создание сессии)
- JoinSessionView (присоединение к сессии)
- WaitingRoomView (ожидание игроков)
- GameOverView (результаты игры)

**Особенности:**
- Градиентный фон (синий-фиолетовый)
- Белое окно по центру с тенью
- Максимальная ширина 400px
- Адаптивные отступы

## GameLayout.vue

**Назначение:** Полноэкранный макет для игровых экранов с картой.

**Используется в:**
- GameSessionView (игровая сессия)

**Особенности:**
- Полноэкранный режим (100vh x 100vw)
- Без отступов и фона
- Подходит для карт и игрового интерфейса

## Использование

```vue
<script setup>
import MainLayout from '@/layouts/MainLayout.vue'
</script>

<template>
  <MainLayout>
    <!-- Ваш контент здесь -->
  </MainLayout>
</template>
```
