<script setup>
/**
 * BaseButton - переиспользуемый компонент кнопки
 *
 * @example
 * <BaseButton variant="primary" size="medium" full-width>Войти</BaseButton>
 * <BaseButton variant="secondary" @click="handleClick">Отмена</BaseButton>
 */

// Пропсы для кастомизации кнопки
defineProps({
  // Вариант кнопки: 'primary', 'secondary'
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary'].includes(value)
  },
  // Размер кнопки: 'small', 'medium', 'large'
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // Отключена ли кнопка
  disabled: {
    type: Boolean,
    default: false
  },
  // Тип кнопки для форм
  type: {
    type: String,
    default: 'button'
  },
  // Полная ширина
  fullWidth: {
    type: Boolean,
    default: false
  }
})

// Эмиты
defineEmits(['click'])
</script>

<template>
  <button
    :type="type"
    :disabled="disabled"
    :class="[
      'base-button',
      `base-button--${variant}`,
      `base-button--${size}`,
      { 'base-button--full-width': fullWidth }
    ]"
    @click="$emit('click', $event)"
  >
    <slot />
  </button>
</template>

<style scoped>
.base-button {
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background 0.2s, color 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-family: inherit;
}

/* Варианты */
.base-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.base-button--primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.base-button--secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.base-button--secondary:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

/* Размеры */
.base-button--small {
  padding: 8px 16px;
  font-size: 14px;
}

.base-button--medium {
  padding: 14px 20px;
  font-size: 16px;
}

.base-button--large {
  padding: 18px 24px;
  font-size: 18px;
}

/* Состояния */
.base-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Полная ширина */
.base-button--full-width {
  width: 100%;
}
</style>
