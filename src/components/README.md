# Base Components

Этот каталог содержит переиспользуемые базовые компоненты для приложения.

## BaseButton.vue

**Назначение:** Универсальная кнопка с различными вариантами оформления.

**Пропсы:**
- `variant` - вариант кнопки: `'primary'`, `'secondary'`
- `size` - размер: `'small'`, `'medium'`, `'large'`
- `disabled` - отключена ли кнопка
- `type` - тип для форм: `'button'`, `'submit'`, `'reset'`
- `fullWidth` - растянуть на всю ширину

**Примеры:**
```vue
<BaseButton variant="primary" full-width>Войти</BaseButton>
<BaseButton variant="secondary" size="small">Отмена</BaseButton>
```

## BaseTextInput.vue

**Назначение:** Универсальное текстовое поле с опциональным лейблом.

**Пропсы:**
- `modelValue` - значение (v-model)
- `label` - лейбл (опциональный)
- `type` - тип поля: `'text'`, `'number'`, `'email'`, etc.
- `placeholder` - плейсхолдер
- `disabled` - отключено ли поле
- `required` - обязательное поле
- `maxlength` - максимальная длина
- `size` - размер: `'small'`, `'medium'`, `'large'`

**Примеры:**
```vue
<BaseTextInput v-model="nickname" label="Никнейм" placeholder="Введите никнейм" />
<BaseTextInput v-model="code" placeholder="Код сессии" :maxlength="10" />
<BaseTextInput v-model="radius" label="Радиус" type="number" />
```

## BaseError.vue

**Назначение:** Универсальный компонент для отображения сообщений (ошибки, предупреждения, успех).

**Пропсы:**
- `message` - текст сообщения (обязательный)
- `type` - тип: `'error'`, `'warning'`, `'success'`, `'info'`
- `closable` - можно ли закрыть (по умолчанию `true`)
- `icon` - кастомная иконка
- `size` - размер: `'small'`, `'medium'`, `'large'`

**События:**
- `@close` - при закрытии сообщения

**Примеры:**
```vue
<BaseError message="Что-то пошло не так" type="error" @close="handleClose" />
<BaseError message="Предупреждение" type="warning" />
<BaseError message="Успех!" type="success" icon="🎉" />
<BaseError message="Информация" type="info" :closable="false" />
```

## BaseLoader.vue

**Назначение:** Полноэкранный лоадер с градиентным фоном.

**Пропсы:**
- `text` - текст под лоадером (по умолчанию "Загрузка...")
- `size` - размер спиннера в пикселях
- `color` - цвет спиннера
- `backgroundColor` - цвет фона спиннера
- `textColor` - цвет текста
- `textSize` - размер шрифта текста
- `borderWidth` - толщина границы спиннера

**Примеры:**
```vue
<BaseLoader v-if="isLoading" />
<BaseLoader text="Подключение..." color="#667eea" />
```

## Принципы использования

1. **Консистентность** - используйте базовые компоненты везде, где это возможно
2. **Кастомизация** - при необходимости используйте CSS классы для дополнительной стилизации
3. **Доступность** - все компоненты поддерживают базовые принципы доступности
4. **Производительность** - компоненты оптимизированы и переиспользуемы

## Цветовая схема

Все компоненты используют единую цветовую схему:
- **Primary:** `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **Error:** `#c33` на фоне `#fee`
- **Warning:** `#f57c00` на фоне `#fff8e1`
- **Success:** `#2e7d32` на фоне `#e8f5e8`
- **Info:** `#1565c0` на фоне `#e3f2fd`
