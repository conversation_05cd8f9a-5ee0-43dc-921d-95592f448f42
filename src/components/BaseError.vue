<script setup>
/**
 * BaseError - переиспользуемый компонент для отображения ошибок
 * 
 * @example
 * <!-- Простая ошибка -->
 * <BaseError message="Что-то пошло не так" />
 * 
 * <!-- Ошибка с возможностью закрытия -->
 * <BaseError message="Неверный пароль" closable @close="handleClose" />
 * 
 * <!-- Ошибка с кастомным типом -->
 * <BaseError message="Предупреждение" type="warning" />
 * 
 * <!-- Ошибка с кастомной иконкой -->
 * <BaseError message="Успех!" type="success" icon="✓" />
 */

// Пропсы для кастомизации ошибки
defineProps({
  // Текст ошибки
  message: {
    type: String,
    required: true
  },
  // Тип ошибки: 'error', 'warning', 'success', 'info'
  type: {
    type: String,
    default: 'error',
    validator: (value) => ['error', 'warning', 'success', 'info'].includes(value)
  },
  // Можно ли закрыть ошибку
  closable: {
    type: Boolean,
    default: true
  },
  // Кастомная иконка
  icon: {
    type: String,
    default: ''
  },
  // Размер: 'small', 'medium', 'large'
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

// Эмиты
defineEmits(['close'])

// Иконки по умолчанию для разных типов
const defaultIcons = {
  error: '⚠️',
  warning: '⚠️',
  success: '✓',
  info: 'ℹ️'
}
</script>

<template>
  <div 
    :class="[
      'base-error',
      `base-error--${type}`,
      `base-error--${size}`
    ]"
  >
    <!-- Иконка -->
    <div class="error-icon">
      {{ icon || defaultIcons[type] }}
    </div>
    
    <!-- Текст ошибки -->
    <div class="error-content">
      <p class="error-text">{{ message }}</p>
    </div>
    
    <!-- Кнопка закрытия -->
    <button 
      v-if="closable"
      class="error-close"
      @click="$emit('close')"
      aria-label="Закрыть"
    >
      ×
    </button>
  </div>
</template>

<style scoped>
.base-error {
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid;
}

/* Типы ошибок */
.base-error--error {
  background: #fee;
  border-color: #fcc;
  color: #c33;
}

.base-error--warning {
  background: #fff8e1;
  border-color: #ffcc02;
  color: #f57c00;
}

.base-error--success {
  background: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.base-error--info {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1565c0;
}

/* Размеры */
.base-error--small {
  padding: 8px;
  margin-bottom: 12px;
}

.base-error--small .error-text {
  font-size: 12px;
}

.base-error--small .error-icon {
  font-size: 14px;
}

.base-error--medium {
  padding: 12px;
  margin-bottom: 20px;
}

.base-error--medium .error-text {
  font-size: 14px;
}

.base-error--medium .error-icon {
  font-size: 16px;
}

.base-error--large {
  padding: 16px;
  margin-bottom: 24px;
}

.base-error--large .error-text {
  font-size: 16px;
}

.base-error--large .error-icon {
  font-size: 18px;
}

/* Элементы */
.error-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-content {
  flex: 1;
}

.error-text {
  margin: 0;
  font-weight: 500;
}

.error-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.base-error--error .error-close:hover {
  background: #fcc;
}

.base-error--warning .error-close:hover {
  background: #ffcc02;
}

.base-error--success .error-close:hover {
  background: #4caf50;
  color: white;
}

.base-error--info .error-close:hover {
  background: #2196f3;
  color: white;
}
</style>
