<script setup>
// Пропсы для кастомизации loader'а
defineProps({
  // Текст под loader'ом
  text: {
    type: String,
    default: 'Загрузка...'
  },
  // Размер loader'а в пикселях
  size: {
    type: Number,
    default: 50
  },
  // Цвет loader'а
  color: {
    type: String,
    default: 'white'
  },
  // Цвет фона loader'а (прозрачная часть)
  backgroundColor: {
    type: String,
    default: 'rgba(255, 255, 255, 0.3)'
  },
  // Цвет текста
  textColor: {
    type: String,
    default: 'white'
  },
  // Размер шрифта текста
  textSize: {
    type: String,
    default: '18px'
  },
  // Толщина границы loader'а
  borderWidth: {
    type: Number,
    default: 4
  }
})
</script>

<template>
  <div class="loader-container">
    <div
      class="loader"
      :style="{
        width: `${size}px`,
        height: `${size}px`,
        border: `${borderWidth}px solid ${backgroundColor}`,
        borderTop: `${borderWidth}px solid ${color}`
      }"
    ></div>
    <p
      v-if="text"
      class="loader-text"
      :style="{
        color: textColor,
        fontSize: textSize
      }"
    >
      {{ text }}
    </p>
  </div>
</template>

<style scoped>
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loader {
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loader-text {
  margin: 0;
}
</style>
