// HTTP сервис для выполнения запросов к API

import { API_BASE_URL, DEFAULT_HEADERS, REQUEST_TIMEOUT } from '@/constants/api.js'

/**
 * Выполнить HTTP запрос с обработкой ошибок
 * @param {string} url - URL для запроса
 * @param {object} options - Опции для fetch
 * @returns {Promise<object>} Ответ сервера
 */
export async function makeRequest(url, options = {}) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT)

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        ...DEFAULT_HEADERS,
        ...options.headers,
      },
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    clearTimeout(timeoutId)

    if (error.name === 'AbortError') {
      throw new Error('Превышено время ожидания запроса')
    }

    throw error
  }
}

/**
 * Выполнить GET запрос
 * @param {string} endpoint - Эндпоинт API (без базового URL)
 * @param {object} options - Дополнительные опции для fetch
 * @returns {Promise<object>} Ответ сервера
 */
export async function get(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`
  return makeRequest(url, {
    method: 'GET',
    ...options,
  })
}

/**
 * Выполнить POST запрос
 * @param {string} endpoint - Эндпоинт API (без базового URL)
 * @param {object} data - Данные для отправки
 * @param {object} options - Дополнительные опции для fetch
 * @returns {Promise<object>} Ответ сервера
 */
export async function post(endpoint, data = null, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`
  return makeRequest(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : null,
    ...options,
  })
}

/**
 * Выполнить PUT запрос
 * @param {string} endpoint - Эндпоинт API (без базового URL)
 * @param {object} data - Данные для отправки
 * @param {object} options - Дополнительные опции для fetch
 * @returns {Promise<object>} Ответ сервера
 */
export async function put(endpoint, data = null, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`
  return makeRequest(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : null,
    ...options,
  })
}

/**
 * Выполнить DELETE запрос
 * @param {string} endpoint - Эндпоинт API (без базового URL)
 * @param {object} options - Дополнительные опции для fetch
 * @returns {Promise<object>} Ответ сервера
 */
export async function del(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`
  return makeRequest(url, {
    method: 'DELETE',
    ...options,
  })
}
