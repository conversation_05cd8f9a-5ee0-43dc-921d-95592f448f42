<script setup>
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseTextInput from '@/components/BaseTextInput.vue'
import BaseError from '@/components/BaseError.vue'

// Пример состояния для демонстрации ошибки
const sessionCode = ref('')
const joinError = ref('')

function handleJoin() {
  if (!sessionCode.value.trim()) {
    joinError.value = 'Введите код сессии'
  } else if (sessionCode.value.length < 6) {
    joinError.value = 'Код сессии должен содержать минимум 6 символов'
  } else {
    joinError.value = 'Сессия не найдена или недоступна'
  }
}
</script>

<template>
  <MainLayout>
    <h1 class="title">Присоединиться к игре</h1>

    <!-- Ошибка присоединения -->
    <BaseError
      v-if="joinError"
      :message="joinError"
      type="error"
      @close="joinError = ''"
    />

    <div class="form">
      <BaseTextInput
        id="sessionCode"
        v-model="sessionCode"
        label="Код игровой сессии"
        type="text"
        placeholder="Введите код сессии"
        :maxlength="10"
        class="session-code-input"
      />

      <div class="button-group">
        <BaseButton variant="primary" full-width @click="handleJoin">Присоединиться</BaseButton>
        <BaseButton variant="secondary" full-width>В главное меню</BaseButton>
      </div>
    </div>
  </MainLayout>
</template>

<style scoped>
.title {
  text-align: center;
  margin: 0 0 30px;
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Специальные стили для кода сессии */
.session-code-input :deep(.input-field) {
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: monospace;
  font-weight: bold;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}


</style>
