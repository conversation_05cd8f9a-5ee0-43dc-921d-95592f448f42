<script setup>
import MainLayout from '@/layouts/MainLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseError from '@/components/BaseError.vue'
</script>

<template>
  <MainLayout>
    <div class="game-over-content">
      <h1 class="title">Игра завершена!</h1>

      <!-- Сообщение об успешном завершении игры -->
      <BaseError
        message="Поздравляем! Игра успешно завершена"
        type="success"
        :closable="false"
        icon="🎉"
      />

      <div class="winner-section">
        <div class="winner-icon">👑</div>
        <h2 class="winner-title">Победитель</h2>
        <div class="winner-name">Игрок1</div>
      </div>

      <div class="results-section">
        <h3 class="section-title">Результаты игры</h3>
        <div class="results-list">
          <div class="result-item">
            <span class="killer">Игрок1</span>
            <span class="action">выбил</span>
            <span class="victim">Игрока2</span>
          </div>
          <div class="result-item">
            <span class="killer">Игрок3</span>
            <span class="action">выбил</span>
            <span class="victim">Игрока4</span>
          </div>
          <div class="result-item">
            <span class="killer">Игрок1</span>
            <span class="action">выбил</span>
            <span class="victim">Игрока3</span>
          </div>
        </div>
      </div>

      <BaseButton variant="primary" full-width>В главное меню</BaseButton>
    </div>
  </MainLayout>
</template>

<style scoped>
.game-over-content {
  text-align: center;
}

.title {
  margin: 0 0 30px;
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.winner-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border-radius: 12px;
  color: #333;
}

.winner-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.winner-title {
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
}

.winner-name {
  font-size: 20px;
  font-weight: bold;
}

.results-section {
  margin-bottom: 30px;
  text-align: left;
}

.section-title {
  margin: 0 0 16px;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.killer {
  font-weight: 600;
  color: #e74c3c;
}

.action {
  color: #666;
  font-style: italic;
}

.victim {
  font-weight: 500;
  color: #333;
}


</style>
