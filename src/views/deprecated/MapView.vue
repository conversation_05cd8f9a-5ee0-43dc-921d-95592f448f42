<script setup>
  import { ref, onMounted } from 'vue'
  import { LMap, LTileLayer, LMarker } from '@vue-leaflet/vue-leaflet'
  import 'leaflet/dist/leaflet.css'

  // 🛠️ Чиним иконки Leaflet в Vite (важно!)
  import L from 'leaflet'
  delete L.Icon.Default.prototype._getIconUrl
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: new URL('leaflet/dist/images/marker-icon-2x.png', import.meta.url).href,
    iconUrl: new URL('leaflet/dist/images/marker-icon.png', import.meta.url).href,
    shadowUrl: new URL('leaflet/dist/images/marker-shadow.png', import.meta.url).href,
  })

  // 🌍 Переменные для карты
  const zoom = ref(15)
  const userPosition = ref([0, 0])
  const tileUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
  const attribution = '&copy; OpenStreetMap contributors'

  // 📍 Геолокация через Capacitor
  import { Capacitor } from '@capacitor/core'
  import { Geolocation } from '@capacitor/geolocation'

  onMounted(async () => {
    try {
      if (Capacitor.getPlatform() !== 'web') {
        await Geolocation.requestPermissions()
      }

      const coords = await Geolocation.getCurrentPosition({ timeout: 5000 })
      userPosition.value = [coords.coords.latitude, coords.coords.longitude]
    } catch (err) {
      console.error('Ошибка геопозиции:', err)
      // fallback на центр Москвы
      userPosition.value = [55.751244, 37.618423]
    }
  })
</script>

<template>
  <l-map
    :zoom="zoom"
    :center="userPosition"
    style="height: 100vh; width: 100vw"
  >
    <l-tile-layer
      :url="tileUrl"
      :attribution="attribution"
    />
    <l-marker :lat-lng="userPosition" />
  </l-map>
</template>

<style scoped>

</style>
