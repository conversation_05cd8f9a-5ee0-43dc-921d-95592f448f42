<script setup>
import GameLayout from '@/layouts/GameLayout.vue'
</script>

<template>
  <GameLayout>
    <!-- Карта (полноэкранная) -->
    <div class="map-container">
      <div class="map-placeholder">
        Карта с зоной и позицией игрока
      </div>
    </div>

    <!-- UI поверх карты -->
    <div class="game-ui">
      <!-- Таймер -->
      <div class="timer-widget">
        <div class="timer-label">До сужения зоны</div>
        <div class="timer-value">02:45</div>
      </div>

      <!-- Кнопки действий -->
      <div class="action-buttons">
        <button class="camera-button">📷</button>
        <button class="menu-button">☰</button>
      </div>
    </div>
  </GameLayout>
</template>

<style scoped>
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #4CAF50 0%, #8BC34A 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.timer-widget {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  text-align: center;
  pointer-events: auto;
}

.timer-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.timer-value {
  font-size: 18px;
  font-weight: bold;
  font-family: monospace;
}

.action-buttons {
  position: absolute;
  bottom: 30px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto;
}

.camera-button,
.menu-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  font-size: 24px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.camera-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.menu-button {
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

.menu-button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}
</style>
