<script setup>
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseTextInput from '@/components/BaseTextInput.vue'
import BaseError from '@/components/BaseError.vue'

// Пример состояния для демонстрации ошибок
const showValidationError = ref(false)
const showWarning = ref(false)

function handleStartGame() {
  // Пример валидации
  showValidationError.value = true
}

function handleShowWarning() {
  showWarning.value = true
}
</script>

<template>
  <MainLayout>
    <h1 class="title">Создать игровую сессию</h1>

    <!-- Примеры ошибок для демонстрации -->
    <BaseError
      v-if="showValidationError"
      message="Пожалуйста, заполните все обязательные поля"
      type="error"
      @close="showValidationError = false"
    />

    <BaseError
      v-if="showWarning"
      message="Рекомендуется установить радиус не менее 500 метров"
      type="warning"
      @close="showWarning = false"
    />

    <div class="form">
      <div class="input-group">
        <label class="input-label">Центр первой зоны</label>
        <div class="map-placeholder">Карта (будет добавлена позже)</div>
      </div>

      <BaseTextInput
        label="Радиус первой зоны (м)"
        type="number"
        placeholder="1000"
      />

      <BaseTextInput
        label="Процент уменьшения зоны (%)"
        type="number"
        placeholder="20"
      />

      <BaseTextInput
        label="Время уменьшения зоны (сек)"
        type="number"
        placeholder="60"
      />

      <BaseTextInput
        label="Задержка перед сжатием (сек)"
        type="number"
        placeholder="30"
      />

      <div class="button-group">
        <BaseButton variant="primary" full-width @click="handleStartGame">Начать игру</BaseButton>
        <BaseButton variant="secondary" full-width @click="handleShowWarning">Показать предупреждение</BaseButton>
        <BaseButton variant="secondary" full-width>В главное меню</BaseButton>
      </div>
    </div>
  </MainLayout>
</template>

<style scoped>
.title {
  text-align: center;
  margin: 0 0 30px;
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.map-placeholder {
  padding: 40px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  text-align: center;
  color: #999;
  background: #f9f9f9;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}


</style>
