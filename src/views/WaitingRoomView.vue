<script setup>
import MainLayout from '@/layouts/MainLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
</script>

<template>
  <MainLayout>
    <h1 class="title">Ожидание игроков</h1>

    <div class="session-info">
      <div class="session-code">
        <span class="label">Код сессии:</span>
        <span class="code">ABC123</span>
      </div>
    </div>

    <div class="players-section">
      <h2 class="section-title">Игроки (3/8)</h2>
      <div class="players-list">
        <div class="player-item owner">
          <span class="player-name">Игрок1</span>
          <span class="player-status">Владелец</span>
        </div>
        <div class="player-item">
          <span class="player-name">Игрок2</span>
          <span class="player-status">Готов</span>
        </div>
        <div class="player-item">
          <span class="player-name">Игрок3</span>
          <span class="player-status">Готов</span>
        </div>
      </div>
    </div>

    <div class="button-group">
      <BaseButton variant="primary" full-width>Начать игру</BaseButton>
      <BaseButton variant="secondary" full-width>Покинуть игру</BaseButton>
    </div>
  </MainLayout>
</template>

<style scoped>
.title {
  text-align: center;
  margin: 0 0 20px;
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.session-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  text-align: center;
}

.session-code {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.label {
  color: #666;
  font-size: 14px;
}

.code {
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.players-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.player-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #ddd;
}

.player-item.owner {
  border-left-color: #667eea;
  background: #f0f2ff;
}

.player-name {
  font-weight: 500;
  color: #333;
}

.player-status {
  font-size: 12px;
  color: #666;
  background: white;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #ddd;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}


</style>
