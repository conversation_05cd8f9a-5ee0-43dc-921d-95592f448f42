<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/authStore.js'
import BaseLoader from '@/components/BaseLoader.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseTextInput from '@/components/BaseTextInput.vue'
import BaseError from '@/components/BaseError.vue'
import MainLayout from '@/layouts/MainLayout.vue'

const authStore = useAuthStore()

// Локальное состояние формы
const nickname = ref('')

// Инициализация при Mount компонента
onMounted(async () => {
  await authStore.initialize()
})

// Обработчик отправки формы
async function handleSubmit() {
  if (!nickname.value.trim()) {
    return
  }

  await authStore.register(nickname.value.trim())
}

// Обработчик очистки ошибки
function handleClearError() {
  authStore.clearError()
}
</script>

<template>
  <!-- Лодер поверх всего экрана -->
  <BaseLoader v-if="authStore.isLoading" />

  <!-- Форма регистрации -->
  <MainLayout v-else-if="!authStore.isAuthenticated && !authStore.isLoading">
    <h1 class="auth-title">Geo Royale</h1>

    <!-- Ошибка -->
    <BaseError
      v-if="authStore.error"
      :message="authStore.error"
      type="error"
      @close="handleClearError"
    />

    <!-- Форма -->
    <form @submit.prevent="handleSubmit" class="form">
      <BaseTextInput
        id="nickname"
        v-model="nickname"
        label="Никнейм"
        type="text"
        placeholder="Введите ваш никнейм"
        :maxlength="20"
        required
        :disabled="authStore.isLoading"
      />

      <BaseButton
        type="submit"
        variant="primary"
        full-width
        :disabled="authStore.isLoading || !nickname.trim()"
      >
        Войти
      </BaseButton>
    </form>
  </MainLayout>
</template>

<style scoped>
.auth-title {
  text-align: center;
  margin: 0 0 30px;
  color: #333;
  font-size: 28px;
  font-weight: bold;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
