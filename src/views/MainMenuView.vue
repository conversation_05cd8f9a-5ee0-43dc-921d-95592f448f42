<script setup>
import MainLayout from '@/layouts/MainLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
</script>

<template>
  <MainLayout>
    <h1 class="title">Geo Royale</h1>

    <div class="menu-buttons">
      <BaseButton variant="primary" full-width>Создать игру</BaseButton>
      <BaseButton variant="primary" full-width>Присоединиться к игре</BaseButton>
    </div>
  </MainLayout>
</template>

<style scoped>
.title {
  text-align: center;
  margin: 0 0 30px;
  color: #333;
  font-size: 28px;
  font-weight: bold;
}

.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
}


</style>
