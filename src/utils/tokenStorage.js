// Утилита для работы с JWT токенами в localStorage

const JWT_KEY = 'georoyale_jwt'

/**
 * Получить JWT токен из localStorage
 * @returns {string|null} JWT токен или null если не найден
 */
export function getToken() {
  try {
    return localStorage.getItem(JWT_KEY)
  } catch (error) {
    console.error('Ошибка при получении токена из localStorage:', error)
    return null
  }
}

/**
 * Сохранить JWT токен в localStorage
 * @param {string} token - JWT токен для сохранения
 */
export function saveToken(token) {
  try {
    if (!token || typeof token !== 'string') {
      throw new Error('Токен должен быть непустой строкой')
    }
    localStorage.setItem(JWT_KEY, token)
  } catch (error) {
    console.error('Ошибка при сохранении токена в localStorage:', error)
    throw error
  }
}

/**
 * Удалить JWT токен из localStorage
 */
export function clearToken() {
  try {
    localStorage.removeItem(JWT_KEY)
  } catch (error) {
    console.error('Ошибка при удалении токена из localStorage:', error)
  }
}
