// Pinia store для управления состоянием авторизации

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

import * as authService from '@/services/authService.js'
import * as tokenStorage from '@/utils/tokenStorage.js'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()

  // Состояние
  const user = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Геттеры
  const isAuthenticated = computed(() => !!user.value)
  const currentUser = computed(() => user.value)

  /**
   * Очистить ошибку
   */
  function clearError() {
    error.value = null
  }

  /**
   * Установить ошибку
   * @param {string} message - Сообщение об ошибке
   */
  function setError(message) {
    error.value = message
    console.error('Auth error:', message)
  }

  /**
   * Инициализация при загрузке приложения
   * Проверяет наличие сохраненного JWT и валидирует его
   */
  async function initialize() {
    if (isLoading.value) return

    isLoading.value = true
    clearError()

    try {
      const savedToken = tokenStorage.getToken()

      if (!savedToken) {
        // Токена нет - показываем форму регистрации
        return
      }

      // Токен есть - проверяем его валидность
      const userData = await authService.getProfile(savedToken)
      user.value = userData

      // Токен валиден - переходим в главное меню
      await router.push('/main-menu')

    } catch (err) {
      // Токен невалиден или ошибка сети - очищаем и показываем форму
      tokenStorage.clearToken()
      user.value = null
      setError('Сессия истекла. Необходимо войти заново.')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Регистрация нового пользователя
   * @param {string} nickname - Никнейм пользователя
   */
  async function register(nickname) {
    if (isLoading.value) return

    isLoading.value = true
    clearError()

    try {
      // Регистрируем пользователя и получаем JWT
      const jwt = await authService.register(nickname)

      // Сохраняем JWT
      tokenStorage.saveToken(jwt)

      // Получаем данные пользователя
      const userData = await authService.getProfile(jwt)
      user.value = userData

      // Переходим в главное меню
      await router.push('/main-menu')

    } catch (err) {
      setError(err.message || 'Ошибка при регистрации')

      // Очищаем токен в случае ошибки
      tokenStorage.clearToken()
      user.value = null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Выход из аккаунта
   */
  async function logout() {
    tokenStorage.clearToken()
    user.value = null
    clearError()

    await router.push('/')
  }

  return {
    // Состояние
    user: currentUser,
    isLoading,
    error,

    // Геттеры
    isAuthenticated,

    // Действия
    initialize,
    register,
    logout,
    clearError,
  }
})
