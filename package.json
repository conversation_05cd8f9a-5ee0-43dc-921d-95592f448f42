{"name": "geo_royale_client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/geolocation": "^7.1.3", "@capacitor/ios": "^7.4.2", "@vue-leaflet/vue-leaflet": "^0.10.1", "leaflet": "^1.9.4", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "prettier": "3.5.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}